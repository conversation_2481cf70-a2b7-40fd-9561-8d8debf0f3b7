import React, { useState, useEffect, useMemo } from "react";
import {
  View,
  Text,
  FlatList,
  Pressable,
  TextInput,
  ActivityIndicator,
  ScrollView, // Keep ScrollView for main emoji grid, but not for category tabs
  Platform,
} from "react-native";
import { cn } from "~/lib/utils";
import emojiMartData from "@emoji-mart/data/sets/14/native.json";
// Icônes temporairement supprimées pour compatibilité web
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";

interface EmojiMartSkin {
  unified: string;
  native: string;
}
interface EmojiMartEntry {
  id: string;
  name: string;
  native?: string;
  keywords: string[];
  shortcodes?: string;
  emoticons?: string[];
  skins: EmojiMartSkin[];
  version?: number;
}

interface EmojiMartCategory {
  id: string;
  name: string;
  emojis: EmojiMartEntry["id"][];
}

export interface PickerEmoji {
  id: string;
  name: string;
  native: string;
  keywords: string[];
  baseId: string;
}

interface EmojiPickerProps {
  onEmojiSelected: (emoji: PickerEmoji) => void;
}

const transformEmojiData = (
  entry: EmojiMartEntry,
  skinIndex?: number
): PickerEmoji | null => {
  let nativeChar: string | undefined;
  let idSuffix = "";
  if (skinIndex !== undefined && entry.skins?.[skinIndex]) {
    nativeChar = entry.skins[skinIndex].native;
    if (skinIndex > 0) {
      idSuffix = `_skin_${skinIndex}`;
    }
  } else if (entry.skins?.[0]) {
    nativeChar = entry.skins[0].native;
  } else if (entry.native) {
    nativeChar = entry.native;
  }
  if (!nativeChar) return null;
  return {
    id: `${entry.id}${idSuffix}`,
    name: entry.name,
    native: nativeChar,
    keywords: entry.keywords || [],
    baseId: entry.id,
  };
};

const getCategoryIcon = (
  categoryId: string,
  categories: EmojiMartCategory[],
  emojiMap: { [id: string]: EmojiMartEntry }
): string | null => {
  if (categoryId === "all") return "All";
  const category = categories.find((cat) => cat.id === categoryId);
  if (category) {
    if (category.emojis.length > 0) {
      const firstEmojiBaseId = category.emojis[0];
      const emojiEntry = emojiMap[firstEmojiBaseId];
      if (emojiEntry?.skins?.[0]?.native) return emojiEntry.skins[0].native;
      if (emojiEntry?.native) return emojiEntry.native;
    }
    return category.name ? category.name.substring(0, 1).toUpperCase() : "??";
  }
  return "??";
};

const SKIN_TONE_COLORS = [
  "#FFDE5C", // Default Yellow
  "#FFE1BB", // Light
  "#FFD0A9", // Medium-Light
  "#D7A579", // Medium
  "#B57D52", // Medium-Dark
  "#8D552A", // Dark
];

export function EmojiPicker({ onEmojiSelected }: EmojiPickerProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("all");
  const [allEmojis, setAllEmojis] = useState<PickerEmoji[]>([]);
  const [categories, setCategories] = useState<EmojiMartCategory[]>([]);
  const [rawEmojiMap, setRawEmojiMap] = useState<{
    [id: string]: EmojiMartEntry;
  }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [currentCategoryName, setCurrentCategoryName] =
    useState("Tous les émojis");
  const [selectedSkinTone, setSelectedSkinTone] = useState<number>(0);
  const [isSkinTonePopoverOpen, setIsSkinTonePopoverOpen] = useState(false);

  useEffect(() => {
    try {
      const loadedCategories = emojiMartData.categories as EmojiMartCategory[];
      const emojisFromData = emojiMartData.emojis as {
        [id: string]: EmojiMartEntry;
      };
      setRawEmojiMap(emojisFromData);
      const processedEmojisMap = new Map<string, PickerEmoji>();
      loadedCategories.forEach((category) => {
        category.emojis.forEach((emojiBaseId) => {
          const emojiEntry = emojisFromData[emojiBaseId];
          if (emojiEntry) {
            const baseEmoji = transformEmojiData(emojiEntry, 0);
            if (baseEmoji && !processedEmojisMap.has(baseEmoji.id)) {
              processedEmojisMap.set(baseEmoji.id, baseEmoji);
            }
            if (emojiEntry.skins && emojiEntry.skins.length > 1) {
              for (let i = 1; i < emojiEntry.skins.length; i++) {
                const skinEmoji = transformEmojiData(emojiEntry, i);
                if (skinEmoji && !processedEmojisMap.has(skinEmoji.id)) {
                  processedEmojisMap.set(skinEmoji.id, skinEmoji);
                }
              }
            }
          }
        });
      });
      const uniqueProcessedEmojis = Array.from(processedEmojisMap.values());
      setAllEmojis(uniqueProcessedEmojis);
      const allCategoryBaseIds = loadedCategories.reduce((acc, cat) => {
        cat.emojis.forEach((id) => acc.add(id));
        return acc;
      }, new Set<string>());
      setCategories([
        { id: "all", name: "All", emojis: Array.from(allCategoryBaseIds) },
        ...loadedCategories,
      ]);
      setIsLoading(false);
    } catch (error) {
      console.error("[EmojiPicker] Error loading emoji data:", error);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (selectedCategoryId === "all") {
      setCurrentCategoryName("Tous les émojis");
    } else {
      const currentCat = categories.find((c) => c.id === selectedCategoryId);
      if (currentCat) {
        setCurrentCategoryName(
          currentCat.name ||
            currentCat.id.charAt(0).toUpperCase() + currentCat.id.slice(1)
        );
      } else {
        setCurrentCategoryName("Emojis");
      }
    }
  }, [selectedCategoryId, categories]);

  const filteredEmojis = useMemo(() => {
    if (!allEmojis.length) return [];
    let emojisToDisplay = allEmojis;

    // Skin tone filtering
    if (selectedSkinTone > 0) {
      // A specific skin tone is selected
      emojisToDisplay = emojisToDisplay.filter((emoji) => {
        const rawEntry = rawEmojiMap[emoji.baseId];
        if (rawEntry && rawEntry.skins && rawEntry.skins.length > 1) {
          // It's an emoji with skin tones, keep only the selected skin tone
          return emoji.id === `${emoji.baseId}_skin_${selectedSkinTone}`;
        }
        // It's an emoji without skin tones, keep it
        return true;
      });
    } else {
      // Default (yellow) skin tone is selected (selectedSkinTone === 0)
      emojisToDisplay = emojisToDisplay.filter((emoji) => {
        const rawEntry = rawEmojiMap[emoji.baseId];
        if (rawEntry && rawEntry.skins && rawEntry.skins.length > 1) {
          // It's an emoji with skin tones, keep only the base (yellow) version
          return !emoji.id.includes("_skin_");
        }
        // It's an emoji without skin tones, keep it
        return true;
      });
    }

    if (selectedCategoryId && selectedCategoryId !== "all") {
      const currentCategory = categories.find(
        (cat) => cat.id === selectedCategoryId
      );
      if (currentCategory) {
        const categoryEmojiBaseIds = new Set(currentCategory.emojis);
        emojisToDisplay = emojisToDisplay.filter((emoji) =>
          categoryEmojiBaseIds.has(emoji.baseId)
        );
      }
    }

    if (searchQuery) {
      const lowerSearchQuery = searchQuery.toLowerCase();
      emojisToDisplay = emojisToDisplay.filter(
        (e) =>
          e.name.toLowerCase().includes(lowerSearchQuery) ||
          e.keywords.some((k) => k.toLowerCase().includes(lowerSearchQuery))
      );
    }
    return emojisToDisplay;
  }, [
    searchQuery,
    selectedCategoryId,
    allEmojis,
    categories,
    selectedSkinTone,
  ]);

  const handleRandomEmoji = () => {
    if (allEmojis.length > 0) {
      const randomIndex = Math.floor(Math.random() * allEmojis.length);
      onEmojiSelected(allEmojis[randomIndex]);
    }
  };

  const handleSkinToneSelect = (
    toneIndex: number,
    event?: import("react-native").GestureResponderEvent // Optional event for stopPropagation
  ) => {
    if (event) {
      event.stopPropagation();
    }
    setSelectedSkinTone(toneIndex);
    setIsSkinTonePopoverOpen(false); // Close popover after selection
  };

  const renderEmojiCell = ({ item }: { item: PickerEmoji }) => {
    if (Platform.OS === "web") {
      // Version simplifiée pour le web sans Tooltip
      return (
        <Pressable
          onPress={() => onEmojiSelected(item)}
          className="p-1 items-center justify-center w-full h-full rounded hover:bg-gray-100 dark:hover:bg-gray-800"
          title={item.name} // Utilise le title HTML natif pour le tooltip
        >
          <Text className="text-2xl">{item.native}</Text>
        </Pressable>
      );
    }

    // Version simplifiée pour mobile sans Tooltip
    return (
      <Pressable
        onPress={() => onEmojiSelected(item)}
        className="p-1 items-center justify-center w-full h-full rounded hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <Text className="text-2xl">{item.native}</Text>
      </Pressable>
    );
  };

  const renderGridItem = ({ item }: { item: PickerEmoji }) => (
    <View className="w-[12.5%] aspect-square">{renderEmojiCell({ item })}</View>
  );

  if (isLoading) {
    return (
      <View className="bg-popover p-4 rounded-lg shadow-xl border border-border w-full items-center justify-center h-[400px]">
        <ActivityIndicator size="large" />
        <Text className="mt-2 text-muted-foreground">Loading Emojis...</Text>
      </View>
    );
  }

  return (
    <View className="bg-popover p-3 rounded-lg shadow-xl border border-border w-[360px] max-h-[500px] h-[500px] flex flex-col">
      {/* Changed w-80 to w-[360px] */}
      {/* Tab View Removed */}
      <View className="flex-row items-center h-9 mb-2.5 mt-8 relative">
        {/* Added pt-2 for spacing previously provided by tabs' mb-2 */}
        <View className="flex-1 flex-row items-center h-full px-2.5 rounded-md border border-input bg-background focus-within:border-primary focus-within:outline-none web:focus-within:ring-2 web:focus-within:ring-ring web:focus-within:ring-offset-2 hover:bg-muted/50 mr-2">
          {/* Added mr-2 */}
          <Text className="text-muted-foreground mr-2">🔍</Text>
          <TextInput
            placeholder="Filtrer..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            className="flex-1 h-full text-foreground text-sm py-0 focus:outline-none web:focus:outline-none"
            placeholderTextColor="hsl(var(--muted-foreground))"
            textAlignVertical="center"
          />
          {searchQuery.length > 0 && (
            <Pressable
              onPress={() => setSearchQuery("")}
              className="p-1 items-center justify-center"
            >
              <Text className="text-muted-foreground">❌</Text>
            </Pressable>
          )}
        </View>
        {Platform.OS === "web" ? (
          <Pressable
            onPress={handleRandomEmoji}
            className="p-2 ml-1.5 items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-800"
            title="Emoji Aléatoire"
          >
            <Text className="text-muted-foreground">🎲</Text>
          </Pressable>
        ) : (
          <Pressable
            onPress={handleRandomEmoji}
            className="p-2 ml-1.5 items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-800"
            aria-label="Choisir un emoji aléatoire"
          >
            <Text className="text-muted-foreground">🎲</Text>
          </Pressable>
        )}
        {Platform.OS === "web" ? (
          <Pressable
            className="p-2 items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-800"
            title="Couleur de Peau"
            onPress={() => setIsSkinTonePopoverOpen(!isSkinTonePopoverOpen)}
          >
            <Text className="text-muted-foreground">✋</Text>
          </Pressable>
        ) : (
          <Popover onOpenChange={setIsSkinTonePopoverOpen}>
            <PopoverTrigger asChild>
              <Pressable
                className="p-2 items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-800"
                aria-label="Choisir la couleur de peau"
                onPress={() => setIsSkinTonePopoverOpen(true)} // Manually open
              >
                <Text className="text-muted-foreground">✋</Text>
              </Pressable>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-1 rounded-md shadow-md">
              <View className="flex-row">
                {SKIN_TONE_COLORS.map((color, index) => (
                  <Pressable
                    key={color}
                    onPress={(event) => handleSkinToneSelect(index, event)}
                    className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <View
                      style={{
                        backgroundColor: color,
                        width: 24,
                        height: 24,
                        borderRadius: 12,
                        margin: 2,
                        borderWidth: selectedSkinTone === index ? 2 : 0,
                        borderColor: "hsl(var(--primary))",
                      }}
                    />
                  </Pressable>
                ))}
              </View>
            </PopoverContent>
          </Popover>
        )}
      </View>
      {selectedCategoryId !== "all" && (
        <Text className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-1.5 px-1">
          {currentCategoryName}
        </Text>
      )}
      <View className="flex-1">
        <FlatList
          data={filteredEmojis}
          renderItem={renderGridItem}
          keyExtractor={(item) => item.id}
          numColumns={8}
          showsVerticalScrollIndicator={true}
          contentContainerClassName="pb-1"
        />
      </View>
      {/* Changed ScrollView to View and updated classes */}
      <View className="flex-row items-center justify-around mt-2 border-t border-border pt-2 pb-1">
        {categories.map((cat) => {
          const categoryIcon = getCategoryIcon(cat.id, categories, rawEmojiMap);
          return (
            <Pressable
              key={cat.id}
              onPress={() => setSelectedCategoryId(cat.id)}
              className={cn(
                "p-1 rounded items-center justify-center h-8 w-8 min-w-[32px]",
                selectedCategoryId === cat.id
                  ? "bg-accent"
                  : "hover:bg-muted/50"
              )}
              aria-label={`Category ${cat.name}`}
            >
              {categoryIcon === "All" ? (
                <Text
                  className={cn(
                    "text-lg",
                    selectedCategoryId === cat.id
                      ? "text-accent-foreground"
                      : "text-muted-foreground opacity-70"
                  )}
                >
                  😊
                </Text>
              ) : (
                <Text
                  className={cn(
                    "text-lg",
                    selectedCategoryId === cat.id
                      ? "text-accent-foreground"
                      : "text-muted-foreground opacity-70"
                  )}
                >
                  {categoryIcon || "?"}
                </Text>
              )}
            </Pressable>
          );
        })}
      </View>
      {/* Skin tone selector for web - positioned at root level to ensure proper z-index */}
      {Platform.OS === "web" && isSkinTonePopoverOpen && (
        <View
          className="absolute bg-popover border border-border rounded-md shadow-lg p-1"
          style={{
            top: 60, // Position it below the search/controls area
            right: 12, // Align with the right edge
            zIndex: 9999,
            elevation: 1000,
          }}
        >
          <View className="flex-row">
            {SKIN_TONE_COLORS.map((color, index) => (
              <Pressable
                key={color}
                onPress={() => handleSkinToneSelect(index)}
                className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <View
                  style={{
                    backgroundColor: color,
                    width: 24,
                    height: 24,
                    borderRadius: 12,
                    margin: 2,
                    borderWidth: selectedSkinTone === index ? 2 : 0,
                    borderColor: "hsl(var(--primary))",
                  }}
                />
              </Pressable>
            ))}
          </View>
        </View>
      )}
    </View>
  );
}
