import React, { useState } from "react";
import { View, Platform, Pressable, Modal } from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

interface DatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  className?: string;
  placeholder?: string;
  error?: boolean;
}

export function DatePicker({
  value,
  onChange,
  minimumDate,
  maximumDate,
  className,
  placeholder = "Sélectionner une date",
  error = false,
}: DatePickerProps) {
  const [showPicker, setShowPicker] = useState(false);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatDateInput = (date: Date) => {
    return date.toISOString().split("T")[0];
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === "android") {
      setShowPicker(false);
    }

    if (selectedDate) {
      onChange(selectedDate);
    }
  };

  const handleWebDateChange = (dateString: string) => {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  if (Platform.OS === "web") {
    return (
      <input
        type="date"
        value={formatDateInput(value)}
        onChange={(e) => handleWebDateChange(e.target.value)}
        min={minimumDate ? formatDateInput(minimumDate) : undefined}
        max={maximumDate ? formatDateInput(maximumDate) : undefined}
        className={cn(
          "h-11 px-3 py-2 border rounded-md bg-background text-foreground",
          "focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
          error ? "border-destructive" : "border-border",
          className
        )}
        placeholder={placeholder}
        style={{
          width: "100%",
          fontSize: "14px",
          fontFamily: "inherit",
        }}
      />
    );
  }

  return (
    <View className={className}>
      <Pressable onPress={() => setShowPicker(true)}>
        <View
          className={cn(
            "h-11 px-3 py-2 border rounded-md bg-background flex-row items-center justify-between",
            error ? "border-destructive" : "border-border"
          )}
        >
          <Text className="text-foreground">{formatDate(value)}</Text>
          <Text className="text-muted-foreground">📅</Text>
        </View>
      </Pressable>

      {Platform.OS === "ios" ? (
        <Modal
          visible={showPicker}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowPicker(false)}
        >
          <View className="flex-1 justify-end bg-black/50">
            <View className="bg-background border-t border-border">
              <View className="flex-row justify-between items-center p-4 border-b border-border">
                <Button variant="ghost" onPress={() => setShowPicker(false)}>
                  <Text>Annuler</Text>
                </Button>
                <Text className="font-medium">Sélectionner une date</Text>
                <Button variant="ghost" onPress={() => setShowPicker(false)}>
                  <Text className="text-primary">Valider</Text>
                </Button>
              </View>
              <DateTimePicker
                value={value}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
                locale="fr-FR"
                style={{ backgroundColor: "transparent" }}
              />
            </View>
          </View>
        </Modal>
      ) : (
        showPicker && (
          <DateTimePicker
            value={value}
            mode="date"
            display="default"
            onChange={handleDateChange}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
            locale="fr-FR"
          />
        )
      )}
    </View>
  );
}
