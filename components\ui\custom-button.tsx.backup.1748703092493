"use client";

import * as React from "react";
import {
  Text,
  View,
  ActivityIndicator,
  Platform,
  Pressable,
} from "react-native";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "~/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface CustomButtonProps extends VariantProps<typeof buttonVariants> {
  onPress?: () => void;
  disabled?: boolean;
  className?: string;
  loading?: boolean;
  children?: React.ReactNode;
}

export function CustomButton({
  className,
  variant,
  size,
  onPress,
  disabled = false,
  loading = false,
  children,
  ...props
}: CustomButtonProps) {
  // Utiliser Pressable pour toutes les plateformes (plus compatible)
  return (
    <Pressable
      onPress={onPress}
      disabled={disabled || loading}
      className={cn(buttonVariants({ variant, size, className }))}
      style={({ pressed }) => [
        {
          opacity: pressed ? 0.8 : 1,
        },
      ]}
      {...props}
    >
      <View className="flex-row items-center justify-center">
        {loading && (
          <ActivityIndicator size="small" color="#ffffff" className="mr-2" />
        )}
        {children}
      </View>
    </Pressable>
  );
}
