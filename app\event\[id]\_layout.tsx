// --- Begin app/event/[id]/_layout.tsx ---
import { Stack } from "expo-router";
import React from "react";
import { Platform } from "react-native";

export default function EventDetailLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: Platform.OS === "web" ? "#ffffff" : undefined,
        },
        headerTitleStyle: {
          fontWeight: "600",
        },
        headerBackTitleVisible: true,
        headerBackButtonDisplayMode: "default",
        gestureEnabled: true,
        animation: Platform.OS === "ios" ? "slide_from_right" : "fade",
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "Détails Événement",
          headerBackTitle: "Événements",
          headerBackButtonDisplayMode: "default",
          presentation: "card",
          headerShown: true,
          gestureEnabled: true,
        }}
      />
      <Stack.Screen
        name="participants"
        options={{
          title: "Gestion des Participants",
          headerShown: true,
          headerBackTitle: "Retour",
          headerBackButtonDisplayMode: "default",
          presentation: "card",
          gestureEnabled: true,
        }}
      />
      <Stack.Screen
        name="distribute"
        options={{
          title: "Répartition des Tâches",
          headerShown: true,
          headerBackTitle: "Retour",
          headerBackButtonDisplayMode: "default",
          presentation: "card",
          gestureEnabled: true,
        }}
      />
      <Stack.Screen
        name="edit"
        options={{
          title: "Modifier l'Événement",
          headerShown: true,
          headerBackTitle: "Retour",
          headerBackButtonDisplayMode: "default",
          presentation: "modal",
          gestureEnabled: true,
        }}
      />
    </Stack>
  );
}

// --- End app/event/[id]/_layout.tsx ---
