{"name": "triparty", "main": "index.js", "version": "1.0.0", "react-native": {"ws": false}, "scripts": {"dev": "expo start -c", "dev:web": "cross-env EXPO_TARGET=web expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "cross-env EXPO_TARGET=web expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css", "migrate-env": "node scripts/migrate-env-security.js", "security-check": "node scripts/migrate-env-security.js", "migrate-buttons": "node scripts/migrate-button-components.js"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@radix-ui/react-slot": "^1.2.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/material-top-tabs": "^7.2.3", "@react-navigation/native": "^7.0.0", "@rn-primitives/accordion": "^1.1.0", "@rn-primitives/alert-dialog": "^1.1.0", "@rn-primitives/aspect-ratio": "^1.1.0", "@rn-primitives/avatar": "~1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/collapsible": "^1.1.0", "@rn-primitives/context-menu": "^1.1.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/dropdown-menu": "^1.1.0", "@rn-primitives/hover-card": "^1.1.0", "@rn-primitives/label": "^1.1.0", "@rn-primitives/menubar": "^1.1.0", "@rn-primitives/navigation-menu": "^1.1.0", "@rn-primitives/popover": "^1.1.0", "@rn-primitives/portal": "~1.1.0", "@rn-primitives/progress": "~1.1.0", "@rn-primitives/radio-group": "^1.1.0", "@rn-primitives/select": "^1.1.0", "@rn-primitives/separator": "^1.1.0", "@rn-primitives/slot": "~1.1.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/table": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/toggle": "^1.1.0", "@rn-primitives/toggle-group": "^1.1.0", "@rn-primitives/tooltip": "~1.1.0", "@rn-primitives/types": "~1.1.0", "@supabase/supabase-js": "^2.49.5-next.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.0.0", "events": "^3.3.0", "expo": "^53.0.9", "expo-linking": "~7.1.4", "expo-navigation-bar": "~4.2.4", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "lucide-react": "^0.507.0", "lucide-react-native": "^0.378.0", "nativewind": "^4.1.23", "node-fetch": "^3.3.2", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.0.6", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "readable-stream": "^4.7.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "text-encoding": "^0.7.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.10", "@types/react-dom": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "typescript": "~5.8.3"}, "private": true}