import React, { useState } from "react";
import { View, Platform, Pressable, Modal } from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

interface TimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  className?: string;
  placeholder?: string;
  error?: boolean;
}

export function TimePicker({
  value,
  onChange,
  className,
  placeholder = "Sélectionner une heure",
  error = false,
}: TimePickerProps) {
  const [showPicker, setShowPicker] = useState(false);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTimeInput = (date: Date) => {
    return `${date.getHours().toString().padStart(2, "0")}:${date
      .getMinutes()
      .toString()
      .padStart(2, "0")}`;
  };

  const handleTimeChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === "android") {
      setShowPicker(false);
    }

    if (selectedDate) {
      onChange(selectedDate);
    }
  };

  const handleWebTimeChange = (timeString: string) => {
    const [hours, minutes] = timeString.split(":").map(Number);
    if (
      !isNaN(hours) &&
      !isNaN(minutes) &&
      hours >= 0 &&
      hours <= 23 &&
      minutes >= 0 &&
      minutes <= 59
    ) {
      const newDate = new Date(value);
      newDate.setHours(hours, minutes);
      onChange(newDate);
    }
  };

  if (Platform.OS === "web") {
    return (
      <input
        type="time"
        value={formatTimeInput(value)}
        onChange={(e) => handleWebTimeChange(e.target.value)}
        className={cn(
          "h-11 px-3 py-2 border rounded-md bg-background text-foreground",
          "focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
          error ? "border-destructive" : "border-border",
          className
        )}
        placeholder={placeholder}
        style={{
          width: "100%",
          fontSize: "14px",
          fontFamily: "inherit",
        }}
      />
    );
  }

  return (
    <View className={className}>
      <Pressable onPress={() => setShowPicker(true)}>
        <View
          className={cn(
            "h-11 px-3 py-2 border rounded-md bg-background flex-row items-center justify-between",
            error ? "border-destructive" : "border-border"
          )}
        >
          <Text className="text-foreground">{formatTime(value)}</Text>
          <Text className="text-muted-foreground">🕐</Text>
        </View>
      </Pressable>

      {Platform.OS === "ios" ? (
        <Modal
          visible={showPicker}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowPicker(false)}
        >
          <View className="flex-1 justify-end bg-black/50">
            <View className="bg-background border-t border-border">
              <View className="flex-row justify-between items-center p-4 border-b border-border">
                <Button variant="ghost" onPress={() => setShowPicker(false)}>
                  <Text>Annuler</Text>
                </Button>
                <Text className="font-medium">Sélectionner une heure</Text>
                <Button variant="ghost" onPress={() => setShowPicker(false)}>
                  <Text className="text-primary">Valider</Text>
                </Button>
              </View>
              <DateTimePicker
                value={value}
                mode="time"
                display="spinner"
                onChange={handleTimeChange}
                locale="fr-FR"
                is24Hour={true}
                style={{ backgroundColor: "transparent" }}
              />
            </View>
          </View>
        </Modal>
      ) : (
        showPicker && (
          <DateTimePicker
            value={value}
            mode="time"
            display="default"
            onChange={handleTimeChange}
            locale="fr-FR"
            is24Hour={true}
          />
        )
      )}
    </View>
  );
}
