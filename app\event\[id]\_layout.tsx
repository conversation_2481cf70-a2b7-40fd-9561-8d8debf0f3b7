// --- Begin app/event/[id]/_layout.tsx ---
import { Stack } from "expo-router";
import React from "react";

export default function EventDetailLayout() {
  return (
    <Stack screenOptions={{ headerShown: true }}>
      <Stack.Screen
        name="index"
        options={{
          title: "Détails Événement",
          headerBackTitle: "Dashboard",
          headerBackButtonDisplayMode: "minimal",
          presentation: "card",
        }}
      />
      <Stack.Screen
        name="participants"
        options={{
          title: "Gestion des Participants",
          headerShown: true,
          headerBackTitle: "Retour",
          headerBackButtonDisplayMode: "minimal",
          presentation: "card",
        }}
      />
      <Stack.Screen
        name="distribute"
        options={{
          title: "Répartition des Tâches",
          headerShown: true,
          headerBackTitle: "Retour",
          headerBackButtonDisplayMode: "minimal",
          presentation: "card",
        }}
      />
      <Stack.Screen
        name="edit"
        options={{
          title: "Modifier l'Événement",
          headerShown: true,
          headerBackTitle: "Retour",
          headerBackButtonDisplayMode: "minimal",
          presentation: "modal",
        }}
      />
    </Stack>
  );
}

// --- End app/event/[id]/_layout.tsx ---
