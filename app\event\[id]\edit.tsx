import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>rollView, Alert, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Dialog, DialogContent, DialogTrigger } from "~/components/ui/dialog";
import { EmojiPicker, PickerEmoji } from "~/components/EmojiPicker";
import { Switch } from "~/components/ui/switch";
import { DatePicker } from "~/components/ui/date-picker";
import { TimePicker } from "~/components/ui/time-picker";
import { cn } from "~/lib/utils";
import { useAuth } from "~/lib/AuthContext";
import { fetchEventDetails, updateEvent } from "~/lib/supabaseCrud";
import { showToast } from "~/lib/toastService";
import { Event } from "~/lib/types";

interface ValidationErrors {
  title?: string;
  dateTime?: string;
}

export default function EditEventScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();

  // États du formulaire
  const [event, setEvent] = useState<Event | null>(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dateTime, setDateTime] = useState(new Date());
  const [location, setLocation] = useState("");
  const [icon, setIcon] = useState("");
  const [allowSuggestions, setAllowSuggestions] = useState(false);
  const [allowPreAssignment, setAllowPreAssignment] = useState(false);

  // États UI
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [showEmojiPickerDialog, setShowEmojiPickerDialog] = useState(false);

  // Charger les données de l'événement
  useEffect(() => {
    const loadEventData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const eventId = parseInt(id as string, 10);
        const eventData = await fetchEventDetails(eventId);

        if (!eventData) {
          showToast("Événement introuvable.", { type: "error" });
          router.back();
          return;
        }

        // Vérifier les permissions
        if (session?.user?.id !== eventData.organizer_id) {
          showToast("Vous n'avez pas les droits pour modifier cet événement.", {
            type: "error",
          });
          router.back();
          return;
        }

        setEvent(eventData);
        setTitle(eventData.title);
        setDescription(eventData.description || "");
        setDateTime(new Date(eventData.date_time));
        setLocation(eventData.location || "");
        setIcon(eventData.icon || "");
        setAllowSuggestions(eventData.allow_suggestions);
        setAllowPreAssignment(eventData.allow_pre_assignment);
      } catch (error) {
        console.error("Error loading event:", error);
        showToast("Erreur lors du chargement.", { type: "error" });
        router.back();
      } finally {
        setLoading(false);
      }
    };

    loadEventData();
  }, [id, session, router]);

  useEffect(() => {
    if (submitAttempted) {
      validateForm();
    }
  }, [title, dateTime, submitAttempted]);

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    if (!title.trim()) {
      newErrors.title = "Le titre est obligatoire";
    }
    const now = new Date();
    now.setSeconds(0, 0);
    const dateToCheck = new Date(dateTime);
    dateToCheck.setSeconds(0, 0);
    const isSameDay =
      dateToCheck.getDate() === now.getDate() &&
      dateToCheck.getMonth() === now.getMonth() &&
      dateToCheck.getFullYear() === now.getFullYear();
    if (isSameDay) {
      const nowTime = now.getHours() * 60 + now.getMinutes() - 5;
      const eventTime = dateToCheck.getHours() * 60 + dateToCheck.getMinutes();
      if (eventTime <= nowTime) {
        newErrors.dateTime =
          "Pour aujourd'hui, l'heure doit être dans le futur";
      }
    } else if (dateToCheck < now) {
      newErrors.dateTime = "La date ne peut pas être dans le passé";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleIconSelect = (selectedEmoji: PickerEmoji) => {
    setIcon(selectedEmoji.native);
    setShowEmojiPickerDialog(false);
  };

  const handleSaveEvent = async () => {
    setSubmitAttempted(true);
    if (!event || !session?.user?.id) return;

    if (!validateForm()) {
      Alert.alert(
        "Formulaire incomplet",
        "Veuillez corriger les erreurs dans le formulaire."
      );
      return;
    }

    try {
      setSaving(true);
      const updateData = {
        title,
        description: description || null,
        date_time: dateTime.toISOString(),
        location: location || null,
        icon: icon || null,
        allow_suggestions: allowSuggestions,
        allow_pre_assignment: allowPreAssignment,
      };

      const updatedEvent = await updateEvent(event.id, updateData);

      if (updatedEvent) {
        showToast("Événement modifié avec succès !", { type: "success" });
        router.back();
      } else {
        throw new Error("Échec de la modification");
      }
    } catch (error) {
      console.error("Error updating event:", error);
      showToast("Erreur lors de la modification.", { type: "error" });
    } finally {
      setSaving(false);
    }
  };

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <Text className="text-sm text-red-500 mt-1">{message}</Text>;
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text>Chargement...</Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text>Événement introuvable</Text>
      </View>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        <View className="mb-8">
          <Text className="text-2xl font-bold text-center mb-2 text-foreground">
            Modifier l'Événement
          </Text>
          <Text className="text-muted-foreground text-center">
            Modifiez les détails de votre événement
          </Text>
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Informations principales
          </Text>
          <View className="mb-5">
            <Label nativeID="titleLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Titre</Text>
            </Label>
            <Input
              nativeID="titleLabel"
              placeholder="Ex: Anniversaire de Gauthier"
              value={title}
              onChangeText={setTitle}
              aria-required="true"
              className={`h-11 ${
                errors.title ? "border-destructive" : "border-border"
              }`}
            />
            <ErrorMessage message={errors.title} />
          </View>
          <View className="mb-5">
            <Label
              nativeID="descLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Description
            </Label>
            <Textarea
              nativeID="descLabel"
              placeholder="Détails supplémentaires (thème, code vestimentaire...)"
              value={description}
              onChangeText={setDescription}
              numberOfLines={3}
              className="border-border"
            />
          </View>
          <View className="mb-2">
            <Label nativeID="dateTimeLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Date et Heure</Text>
            </Label>
            <View
              className={Platform.OS === "web" ? "flex-row gap-4" : "gap-4"}
            >
              <View className={Platform.OS === "web" ? "flex-1" : "mb-4"}>
                <Label
                  nativeID="dateLabel"
                  className="mb-1.5 font-medium text-foreground"
                >
                  Date
                </Label>
                <DatePicker
                  value={dateTime}
                  onChange={setDateTime}
                  minimumDate={new Date()}
                  error={!!errors.dateTime}
                  placeholder="Sélectionner une date"
                />
              </View>
              <View className={Platform.OS === "web" ? "flex-1" : ""}>
                <Label
                  nativeID="timeLabel"
                  className="mb-1.5 font-medium text-foreground"
                >
                  Heure
                </Label>
                <TimePicker
                  value={dateTime}
                  onChange={setDateTime}
                  error={!!errors.dateTime}
                  placeholder="Sélectionner une heure"
                />
              </View>
            </View>
            <ErrorMessage message={errors.dateTime} />
          </View>
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Détails supplémentaires
          </Text>
          <View className="mb-5">
            <Label
              nativeID="locationLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Lieu
            </Label>
            <Input
              nativeID="locationLabel"
              placeholder="Ex: 12 Rue de la Paix, Paris"
              value={location}
              onChangeText={setLocation}
              className="h-11 border-border"
            />
          </View>

          <View className="mb-5">
            <Label
              nativeID="iconLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Icône
            </Label>
            <Button
              variant="outline"
              className="h-12 w-full flex-row items-center justify-between px-3 py-2"
              onPress={() => setShowEmojiPickerDialog(true)}
            >
              <Text
                className={cn(
                  "text-sm",
                  icon ? "text-foreground" : "text-muted-foreground"
                )}
              >
                {icon || "Choisir une icône"}
              </Text>
              {icon ? (
                <Text className="text-2xl">{icon}</Text>
              ) : (
                <Text className="text-muted-foreground text-xl">😊</Text>
              )}
            </Button>
            <Dialog
              open={showEmojiPickerDialog}
              onOpenChange={setShowEmojiPickerDialog}
            >
              <DialogContent className="p-0 w-auto max-w-md bg-transparent border-none shadow-none z-[9999] web:z-[999999999]">
                <EmojiPicker onEmojiSelected={handleIconSelect} />
              </DialogContent>
            </Dialog>
          </View>
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-8 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Options
          </Text>
          <View className="space-y-4">
            <View className="p-3 bg-muted/50 rounded-lg">
              <View className="flex-row items-start justify-between gap-3">
                <View className="flex-1">
                  <Label
                    nativeID="suggestionsLabel"
                    className="font-medium text-foreground"
                  >
                    Autoriser les suggestions d'items par les invités ?
                  </Label>
                  <Text className="text-sm text-muted-foreground mt-1">
                    Les participants pourront proposer des items
                  </Text>
                </View>
                <View className="shrink-0 mt-1">
                  <Switch
                    nativeID="suggestionsLabel"
                    checked={allowSuggestions}
                    onCheckedChange={setAllowSuggestions}
                  />
                </View>
              </View>
            </View>
            <View className="p-3 bg-muted/50 rounded-lg">
              <View className="flex-row items-start justify-between gap-3">
                <View className="flex-1">
                  <Label
                    nativeID="preassignLabel"
                    className="font-medium text-foreground"
                  >
                    Autoriser la pré-attribution ("Fixer")
                  </Label>
                  <Text className="text-sm text-muted-foreground mt-1">
                    Les invités peuvent réserver des items à l'avance
                  </Text>
                </View>
                <View className="shrink-0 mt-1">
                  <Switch
                    nativeID="preassignLabel"
                    checked={allowPreAssignment}
                    onCheckedChange={setAllowPreAssignment}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>

        <View className="flex-row gap-3">
          <Button
            onPress={() => router.back()}
            variant="outline"
            className="flex-1 h-12"
          >
            <Text>Annuler</Text>
          </Button>
          <Button
            onPress={handleSaveEvent}
            disabled={saving}
            loading={saving}
            className="flex-1 h-12 bg-primary"
          >
            <Text className="text-primary-foreground font-medium">
              {saving ? "Sauvegarde..." : "Sauvegarder"}
            </Text>
          </Button>
        </View>
      </View>
    </ScrollView>
  );
}
