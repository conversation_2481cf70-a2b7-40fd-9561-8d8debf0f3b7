import React, { useState, useEffect } from "react";
import {
  View,
  ActivityIndicator,
  Alert,
  ScrollView,
  Platform,
} from "react-native";
import { Text } from "~/components/ui/text";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Button } from "~/components/ui/button";
import {
  FormLayout,
  FormSection,
  FormField,
} from "~/components/ui/form-layout";
// Use generated types directly
import { Database, Constants } from "~/lib/database.types"; // Import Constants too
import { createItem, updateItem, ItemInsert } from "~/lib/supabaseCrud";
import { showToast } from "~/lib/toastService";
import { cn } from "~/lib/utils";
import { RobustSelect, SelectOption } from "~/components/ui/robust-select";

// Define aliases for generated types used here
type BaseItem = Database["public"]["Tables"]["items"]["Row"];
type CostEnumUnion = Database["public"]["Enums"]["cost_enum"];
type EffortEnumUnion = Database["public"]["Enums"]["effort_enum"];

interface AddItemFormProps {
  eventId: number;
  itemToEdit?: BaseItem | null; // Use BaseItem type from generated types
  onClose: () => void;
  onItemSaved: () => void;
}

// Helper for enum options using generated types and runtime Constants
const costOptions: SelectOption[] = Constants.public.Enums.cost_enum.map(
  (value) => ({
    label: value, // Display €, €€, €€€
    value: value,
  })
);
const effortOptions: SelectOption[] = Constants.public.Enums.effort_enum.map(
  (value) => ({
    label:
      value === "1" ? "1 (Faible)" : value === "2" ? "2 (Moyen)" : "3 (Élevé)", // Keep descriptive label
    value: value,
  })
);

// Predefined categories
const predefinedCategories = [
  "Nourriture",
  "Boissons",
  "Matériel",
  "Décoration",
  "Animation",
  "Autre",
];

const categoryOptions: SelectOption[] = predefinedCategories.map((cat) => ({
  label: cat,
  value: cat,
}));

export function AddItemForm({
  eventId,
  itemToEdit,
  onClose,
  onItemSaved,
}: AddItemFormProps) {
  const [name, setName] = useState("");
  const [categoryOption, setCategoryOption] = useState<SelectOption | null>(
    null
  );
  const [customCategory, setCustomCategory] = useState("");
  const [costOption, setCostOption] = useState<SelectOption | null>(null);
  const [effortOption, setEffortOption] = useState<SelectOption | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ name?: string }>({});

  const isEditMode = !!itemToEdit;

  useEffect(() => {
    if (isEditMode && itemToEdit) {
      setName(itemToEdit.name);
      // Pre-fill category
      if (itemToEdit.category) {
        const foundCategory = predefinedCategories.includes(itemToEdit.category)
          ? itemToEdit.category
          : "Autre";
        setCategoryOption({ label: foundCategory, value: foundCategory });
        setCustomCategory(foundCategory === "Autre" ? itemToEdit.category : "");
      } else {
        setCategoryOption(null);
        setCustomCategory("");
      }
      // Pre-fill cost using generated enum values
      setCostOption(
        itemToEdit.estimated_cost
          ? costOptions.find((o) => o.value === itemToEdit.estimated_cost) ||
              null
          : null
      );
      // Pre-fill effort using generated enum values
      setEffortOption(
        itemToEdit.estimated_effort
          ? effortOptions.find(
              (o) => o.value === itemToEdit.estimated_effort
            ) || null
          : null
      );
    } else {
      // Reset fields for add mode
      setName("");
      setCategoryOption(null);
      setCustomCategory("");
      setCostOption(null);
      setEffortOption(null);
      setErrors({});
    }
  }, [isEditMode, itemToEdit]);

  const validateForm = () => {
    const newErrors: { name?: string } = {};
    if (!name.trim()) {
      newErrors.name = "Le nom de l'item est obligatoire.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert("Formulaire incomplet", "Veuillez corriger les erreurs.");
      return;
    }

    setLoading(true);
    const finalCategoryValue = categoryOption?.value;
    const finalCategory =
      finalCategoryValue === "Autre"
        ? customCategory.trim()
        : finalCategoryValue;

    // Use generated types for data payload
    const commonData = {
      name: name.trim(),
      category: finalCategory || null,
      estimated_cost: costOption?.value as CostEnumUnion | null, // Use generated enum type
      estimated_effort: effortOption?.value as EffortEnumUnion | null, // Use generated enum type
    };

    try {
      if (isEditMode && itemToEdit) {
        const updated = await updateItem(itemToEdit.id, commonData);
        if (updated) {
          showToast("Item modifié avec succès !", { type: "success" });
          onItemSaved();
          onClose();
        } else {
          throw new Error("Échec de la modification de l'item");
        }
      } else {
        const itemDataForCreate: ItemInsert = {
          ...commonData,
          event_id: eventId,
          is_suggestion: false,
          is_personal: false,
          suggester_id: null,
          assigned_participant_id: null,
          fixed_by_participant_id: null,
        };
        const newItem = await createItem(itemDataForCreate);
        if (newItem) {
          showToast("Item ajouté avec succès !", { type: "success" });
          onItemSaved();
          onClose();
        } else {
          throw new Error("Échec de la création de l'item");
        }
      }
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "creating"} item:`,
        error
      );
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      showToast(`Erreur: ${errorMessage}`, { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View
      className="bg-gray-50 w-full min-h-full"
      style={{
        height: Platform.OS === "web" ? "100vh" : "100%",
        overflow: Platform.OS === "web" ? "hidden" : "visible",
      }}
    >
      {/* Header - Design System Correct */}
      <View className="bg-white border-b border-border shadow-sm">
        <View className="flex-row justify-between items-center p-6">
          <View>
            <Text className="text-xl font-bold text-foreground">
              {isEditMode ? "Modifier l'Item" : "Ajouter un Item"}
            </Text>
            <Text className="text-sm text-muted-foreground mt-1">
              {isEditMode
                ? "Modifiez les informations de l'item"
                : "Ajoutez un nouvel item à votre événement"}
            </Text>
          </View>
          <Button
            variant="ghost"
            size="sm"
            onPress={onClose}
            className="rounded-full w-10 h-10"
          >
            <Text className="text-xl text-muted-foreground">✕</Text>
          </Button>
        </View>
      </View>

      {/* Form Content - SCROLL FIXÉ DÉFINITIVEMENT */}
      <ScrollView
        className="flex-1"
        style={{ flex: 1 }}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: Platform.OS === "web" ? 100 : 50,
          minHeight: Platform.OS === "web" ? "100%" : "auto",
        }}
        showsVerticalScrollIndicator={Platform.OS !== "web"}
        scrollEnabled={true}
        nestedScrollEnabled={true}
        keyboardShouldPersistTaps="handled"
      >
        <FormLayout maxWidth="lg" padding="lg">
          <FormSection title="Informations principales">
            <FormField label="Nom de l'item" required error={errors.name}>
              <Input
                nativeID="itemName"
                placeholder="Ex: Salade composée, Boissons, Décoration..."
                value={name}
                onChangeText={setName}
                className={cn(
                  "h-12 rounded-md border px-4",
                  errors.name
                    ? "border-destructive focus:border-destructive"
                    : "border-input focus:border-primary"
                )}
              />
            </FormField>

            <FormField label="Catégorie">
              <RobustSelect
                options={categoryOptions}
                value={categoryOption}
                onValueChange={setCategoryOption}
                placeholder="Choisir une catégorie"
                label="Catégorie"
              />
              {categoryOption && categoryOption.value === "Autre" && (
                <Input
                  placeholder="Préciser la catégorie personnalisée"
                  value={customCategory}
                  onChangeText={setCustomCategory}
                  className="mt-3 h-12 rounded-md border border-input px-4 focus:border-primary"
                />
              )}
            </FormField>
          </FormSection>

          <FormSection
            title="Estimation"
            description="Ces informations aident à mieux organiser l'événement"
          >
            <View className="flex-row gap-4">
              <View className="flex-1">
                <FormField label="Coût Estimé">
                  <RobustSelect
                    options={costOptions}
                    value={costOption}
                    onValueChange={setCostOption}
                    placeholder="Coût"
                    label="Coût"
                  />
                </FormField>
              </View>
              <View className="flex-1">
                <FormField label="Effort Estimé">
                  <RobustSelect
                    options={effortOptions}
                    value={effortOption}
                    onValueChange={setEffortOption}
                    placeholder="Effort"
                    label="Effort"
                  />
                </FormField>
              </View>
            </View>
          </FormSection>

          {/* Actions */}
          <View className="bg-card rounded-lg shadow-sm border border-border p-6">
            <View className="flex-row justify-end gap-4">
              <Button
                variant="outline"
                onPress={onClose}
                disabled={loading}
                className="flex-1 h-12"
              >
                <Text className="font-medium">Annuler</Text>
              </Button>
              <Button
                onPress={handleSubmit}
                disabled={loading}
                loading={loading}
                className="flex-1 h-12"
              >
                {loading ? (
                  <ActivityIndicator color="white" className="mr-2" />
                ) : null}
                <Text className="text-primary-foreground font-medium">
                  {isEditMode ? "Enregistrer" : "Ajouter"}
                </Text>
              </Button>
            </View>
          </View>
        </FormLayout>
      </ScrollView>
    </View>
  );
}
