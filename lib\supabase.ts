// --- Begin lib/supabase.ts ---
import { AppState, Platform } from "react-native";
import "react-native-url-polyfill/auto";
import { createClient, SupabaseClient } from "@supabase/supabase-js"; // Import SupabaseClient
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Database } from "./database.types"; // Import generated types

// Charger les variables d'environnement via le mécanisme intégré d'Expo
const supabaseUrl =
  process.env.EXPO_PUBLIC_SUPABASE_URL ||
  "https://your-supabase-url.supabase.co";
const supabaseAnonKey =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "your-anon-key";

// ⚠️ SÉCURITÉ: La clé service ne doit PAS être exposée côté client
// Elle est uniquement utilisée pour les opérations admin côté serveur
// Sur le web, on utilise la clé anon avec des politiques RLS appropriées
const supabaseServiceKey =
  Platform.OS === "web"
    ? supabaseAnonKey // Sur le web, utiliser la clé anon pour éviter l'exposition
    : process.env.SUPABASE_SERVICE_ROLE_KEY ||
      process.env.EXPO_PUBLIC_SUPABASE_SERVICE_KEY || // Fallback temporaire pour compatibilité
      "your-service-key";

// Debug désactivé pour éviter l'exposition des clés en production

// Vérification que les clés sont bien chargées
if (supabaseUrl === "https://your-supabase-url.supabase.co") {
  console.warn(
    "Using default Supabase URL. Set EXPO_PUBLIC_SUPABASE_URL in your .env file for production."
  );
}
if (supabaseAnonKey === "your-anon-key") {
  console.warn(
    "Using default Supabase Anon Key. Set EXPO_PUBLIC_SUPABASE_ANON_KEY in your .env file for production."
  );
}

// ⚠️ AVERTISSEMENT SÉCURITÉ: Vérifier que la clé service n'est pas exposée
if (process.env.EXPO_PUBLIC_SUPABASE_SERVICE_KEY && Platform.OS === "web") {
  console.error(
    "🚨 SÉCURITÉ: La clé service Supabase est exposée côté client ! " +
      "Utilisez SUPABASE_SERVICE_ROLE_KEY au lieu de EXPO_PUBLIC_SUPABASE_SERVICE_KEY"
  );
}
if (supabaseServiceKey === "your-service-key") {
  console.warn(
    "Using default Supabase Service Key. Set SUPABASE_SERVICE_ROLE_KEY in your .env file for production."
  );
}

// Vrai singleton pattern pour éviter les instances multiples GoTrueClient
class SupabaseSingleton {
  private static instance: SupabaseSingleton;
  private _supabase: SupabaseClient<Database> | null = null;
  private _supabaseAdmin: SupabaseClient<Database> | null = null;
  private _initialized = false;

  private constructor() {}

  public static getInstance(): SupabaseSingleton {
    if (!SupabaseSingleton.instance) {
      SupabaseSingleton.instance = new SupabaseSingleton();
    }
    return SupabaseSingleton.instance;
  }

  public get supabase(): SupabaseClient<Database> {
    if (!this._supabase) {
      this._supabase = this.createSupabaseClient();
      this._initialized = true;
    }
    return this._supabase;
  }

  public get supabaseAdmin(): SupabaseClient<Database> {
    if (!this._supabaseAdmin) {
      this._supabaseAdmin = this.createSupabaseAdminClient();
    }
    return this._supabaseAdmin;
  }

  public isInitialized(): boolean {
    return this._initialized;
  }

  private createSupabaseClient(): SupabaseClient<Database> {
    if (Platform.OS !== "web") {
      return createClient<Database>(supabaseUrl, supabaseAnonKey, {
        auth: {
          storage: AsyncStorage,
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
      });
    } else {
      return createClient<Database>(supabaseUrl, supabaseAnonKey, {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
      });
    }
  }

  private createSupabaseAdminClient(): SupabaseClient<Database> {
    return createClient<Database>(supabaseUrl, supabaseServiceKey, {
      auth: {
        persistSession: false,
      },
    });
  }
}

// Exporter les instances via le singleton (lazy loading)
const supabaseInstance = SupabaseSingleton.getInstance();

// Lazy loading pour éviter l'initialisation multiple
let _supabaseClient: SupabaseClient<Database> | null = null;
let _supabaseAdminClient: SupabaseClient<Database> | null = null;

export const supabase = new Proxy({} as SupabaseClient<Database>, {
  get(target, prop) {
    if (!_supabaseClient) {
      _supabaseClient = supabaseInstance.supabase;
    }
    return (_supabaseClient as any)[prop];
  },
});

export const supabaseAdmin = new Proxy({} as SupabaseClient<Database>, {
  get(target, prop) {
    if (!_supabaseAdminClient) {
      _supabaseAdminClient = supabaseInstance.supabaseAdmin;
    }
    return (_supabaseAdminClient as any)[prop];
  },
});

// Tells Supabase Auth to continuously refresh the session automatically
// if the app is in the foreground. When this is added, you will continue
// to receive `onAuthStateChange` events with the `TOKEN_REFRESHED` or
// `SIGNED_OUT` event if the user's session is terminated. This should
// only be registered once.
if (Platform.OS !== "web") {
  AppState.addEventListener("change", (state) => {
    if (state === "active") {
      // Utiliser l'instance directe pour éviter les proxies
      _supabaseClient?.auth.startAutoRefresh();
    } else {
      _supabaseClient?.auth.stopAutoRefresh();
    }
  });
}
// --- End lib/supabase.ts ---
