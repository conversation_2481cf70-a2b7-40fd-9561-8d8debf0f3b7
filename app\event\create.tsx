import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>View, Alert, Platform } from "react-native";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { DateTimePicker } from "~/components/ui/datetime-picker";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog"; // Import Dialog components
import { EmojiPicker, PickerEmoji } from "~/components/EmojiPicker"; // Import EmojiPicker
// Icône temporairement supprimée pour compatibilité web
import { cn } from "~/lib/utils"; // Import cn utility

import { useAuth } from "~/lib/AuthContext";
import { createEvent, createParticipant } from "~/lib/supabaseCrud";

import { useRouter } from "expo-router";
import { showToast } from "~/lib/toastService";
import { Switch } from "~/components/ui/switch";
// Assuming ParticipantRole and ParticipantStatus are now string literal unions from database.types.ts
// If they are still enums from lib/types.ts, adjust import if needed.
// For now, assuming they are string literals as per recent refactoring.
// import { ParticipantRole, ParticipantStatus } from "~/lib/types";
type ParticipantRoleEnum = Database["public"]["Enums"]["participant_role"];
type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];
import { Database } from "~/lib/database.types";

// Interface pour les erreurs de validation
interface ValidationErrors {
  title?: string;
  dateTime?: string;
}

export default function CreateEventScreen() {
  const { session } = useAuth();
  const router = useRouter();

  // États du formulaire
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dateTime, setDateTime] = useState(new Date());
  const [location, setLocation] = useState("");
  const [icon, setIcon] = useState(""); // Will store the native emoji string
  const [allowSuggestions, setAllowSuggestions] = useState(false);
  const [allowPreAssignment, setAllowPreAssignment] = useState(false);

  // États pour les participants
  const [participantEmail, setParticipantEmail] = useState("");
  const [participantEmails, setParticipantEmails] = useState<string[]>([]);

  // États UI
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);
  const [showEmojiPickerDialog, setShowEmojiPickerDialog] = useState(false);

  useEffect(() => {
    if (submitAttempted) {
      validateForm();
    }
  }, [title, dateTime, submitAttempted]);

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    if (!title.trim()) {
      newErrors.title = "Le titre est obligatoire";
    }
    const now = new Date();
    now.setSeconds(0, 0);
    const dateToCheck = new Date(dateTime);
    dateToCheck.setSeconds(0, 0);
    const isSameDay =
      dateToCheck.getDate() === now.getDate() &&
      dateToCheck.getMonth() === now.getMonth() &&
      dateToCheck.getFullYear() === now.getFullYear();
    if (isSameDay) {
      const nowTime = now.getHours() * 60 + now.getMinutes() - 5;
      const eventTime = dateToCheck.getHours() * 60 + dateToCheck.getMinutes();
      if (eventTime <= nowTime) {
        newErrors.dateTime =
          "Pour aujourd'hui, l'heure doit être dans le futur";
      }
    } else if (dateToCheck < now) {
      newErrors.dateTime = "La date ne peut pas être dans le passé";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleIconSelect = (selectedEmoji: PickerEmoji) => {
    setIcon(selectedEmoji.native);
    setShowEmojiPickerDialog(false); // Close the dialog
  };

  const addParticipant = () => {
    const email = participantEmail.trim().toLowerCase();
    if (!email) return;

    // Validation email basique
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showToast("Veuillez entrer une adresse email valide.", { type: "error" });
      return;
    }

    // Vérifier si l'email n'est pas déjà ajouté
    if (participantEmails.includes(email)) {
      showToast("Cette adresse email est déjà ajoutée.", { type: "error" });
      return;
    }

    setParticipantEmails([...participantEmails, email]);
    setParticipantEmail("");
    showToast("Participant ajouté !", { type: "success" });
  };

  const removeParticipant = (emailToRemove: string) => {
    setParticipantEmails(
      participantEmails.filter((email) => email !== emailToRemove)
    );
  };

  const handleCreateEvent = async () => {
    setSubmitAttempted(true);
    setCreationError(null);
    if (!session?.user?.id) {
      showToast("Vous devez être connecté pour créer un événement.", {
        type: "error",
      });
      return;
    }
    if (!validateForm()) {
      Alert.alert(
        "Formulaire incomplet",
        "Veuillez corriger les erreurs dans le formulaire."
      );
      return;
    }

    try {
      setLoading(true);
      const eventData = {
        title,
        description: description || null,
        date_time: dateTime.toISOString(),
        location: location || null,
        icon: icon || null,
        allow_suggestions: allowSuggestions,
        allow_pre_assignment: allowPreAssignment,
        organizer_delegated: false,
      };

      const createdEvent = await createEvent(eventData, session.user.id);

      if (!createdEvent) {
        setCreationError(
          "Impossible de créer l'événement. Veuillez réessayer."
        );
        showToast("Erreur lors de la création de l'événement.", {
          type: "error",
        });
        setLoading(false);
        return;
      }

      const participantData = {
        event_id: createdEvent.id,
        user_id: session.user.id,
        role: "organizer" as ParticipantRoleEnum, // Use string literal
        status: "accepted" as ParticipantStatusEnum, // Use string literal
        anonymous_name: null,
        anonymous_email: null,
        anonymous_phone: null,
        invitation_token: null,
      };

      const participantResult = await createParticipant(participantData);

      if (participantResult) {
        // Créer les invitations pour les participants ajoutés
        if (participantEmails.length > 0) {
          console.log("Création des invitations pour:", participantEmails);

          for (const email of participantEmails) {
            try {
              // Générer un token unique pour chaque invitation
              const invitationToken = crypto.randomUUID();

              // Créer un participant avec email anonyme
              const participantData = {
                event_id: createdEvent.id,
                user_id: null, // Participant anonyme pour l'instant
                role: "guest" as ParticipantRoleEnum,
                status: "pending" as ParticipantStatusEnum,
                anonymous_name: null,
                anonymous_email: email,
                anonymous_phone: null,
                invitation_token: invitationToken,
              };

              await createParticipant(participantData);
              console.log(`Invitation créée pour ${email}`);
            } catch (error) {
              console.error(`Erreur lors de l'invitation de ${email}:`, error);
            }
          }

          showToast(`${participantEmails.length} invitations créées !`, {
            type: "success",
          });
        }

        showToast("Événement créé avec succès !", { type: "success" });

        // Reset du formulaire
        setTitle("");
        setDescription("");
        setDateTime(new Date());
        setLocation("");
        setIcon("");
        setAllowSuggestions(false);
        setAllowPreAssignment(false);
        setParticipantEmail("");
        setParticipantEmails([]);
        setSubmitAttempted(false);

        // Redirection vers la page de l'événement créé
        router.push(`/event/${createdEvent.id}`);
      } else {
        setCreationError(
          "L'événement a été créé mais vous n'avez pas été ajouté comme organisateur."
        );
        showToast(
          "Événement créé, mais erreur lors de l'ajout de l'organisateur.",
          { type: "error" }
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      setCreationError(`Une erreur est survenue: ${errorMessage}`);
      showToast(`Erreur inattendue: ${errorMessage}`, { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <Text className="text-sm text-destructive mt-1">{message}</Text>;
  };

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        <View className="mb-8">
          <Text className="text-2xl font-bold text-center mb-2 text-foreground">
            Nouvel Événement
          </Text>
          <Text className="text-muted-foreground text-center">
            Créez votre événement en quelques étapes
          </Text>
        </View>

        {creationError && (
          <View className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg mb-6">
            <Text className="text-destructive">{creationError}</Text>
          </View>
        )}

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Informations principales
          </Text>
          <View className="mb-5">
            <Label nativeID="titleLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Titre</Text>
            </Label>
            <Input
              nativeID="titleLabel"
              placeholder="Ex: Anniversaire de Gauthier"
              value={title}
              onChangeText={setTitle}
              aria-required="true"
              className={`h-11 ${
                errors.title ? "border-destructive" : "border-border"
              }`}
            />
            <ErrorMessage message={errors.title} />
          </View>
          <View className="mb-5">
            <Label
              nativeID="descLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Description
            </Label>
            <Textarea
              nativeID="descLabel"
              placeholder="Détails supplémentaires (thème, code vestimentaire...)"
              value={description}
              onChangeText={setDescription}
              numberOfLines={3}
              className="border-border"
            />
          </View>
          <DateTimePicker
            value={dateTime}
            onChange={setDateTime}
            minimumDate={new Date()}
            error={!!errors.dateTime}
            errorMessage={errors.dateTime}
            required={true}
            label="Date et Heure"
            className="mb-2"
          />
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Détails supplémentaires
          </Text>
          <View className="mb-5">
            <Label
              nativeID="locationLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Lieu
            </Label>
            <Input
              nativeID="locationLabel"
              placeholder="Ex: 12 Rue de la Paix, Paris"
              value={location}
              onChangeText={setLocation}
              className="h-11 border-border"
            />
          </View>

          {/* Icône (Emoji Picker) */}
          <View className="mb-5">
            <Label
              nativeID="iconLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Icône
            </Label>
            <Button
              variant="outline"
              className="h-12 w-full flex-row items-center justify-between px-3 py-2"
              onPress={() => setShowEmojiPickerDialog(true)}
            >
              <Text
                className={cn(
                  "text-sm",
                  icon ? "text-foreground" : "text-muted-foreground"
                )}
              >
                {icon || "Choisir une icône"}
              </Text>
              {icon ? (
                <Text className="text-2xl">{icon}</Text>
              ) : (
                <Text className="text-muted-foreground text-xl">😊</Text>
              )}
            </Button>
            <Dialog
              open={showEmojiPickerDialog}
              onOpenChange={setShowEmojiPickerDialog}
            >
              <DialogContent className="p-0 w-auto max-w-md bg-transparent border-none shadow-none z-[9999] web:z-[999999999]">
                <EmojiPicker onEmojiSelected={handleIconSelect} />
              </DialogContent>
            </Dialog>
          </View>
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-8 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Options
          </Text>
          <View className="space-y-4">
            <View className="p-3 bg-muted/50 rounded-lg">
              <View className="flex-row items-start justify-between gap-3">
                <View className="flex-1">
                  <Label
                    nativeID="suggestionsLabel"
                    className="font-medium text-foreground"
                  >
                    Autoriser les suggestions d'items par les invités ?
                  </Label>
                  <Text className="text-sm text-muted-foreground mt-1">
                    Les participants pourront proposer des items
                  </Text>
                </View>
                <View className="shrink-0 mt-1">
                  <Switch
                    nativeID="suggestionsLabel"
                    checked={allowSuggestions}
                    onCheckedChange={setAllowSuggestions}
                  />
                </View>
              </View>
            </View>
            <View className="p-3 bg-muted/50 rounded-lg">
              <View className="flex-row items-start justify-between gap-3">
                <View className="flex-1">
                  <Label
                    nativeID="preassignLabel"
                    className="font-medium text-foreground"
                  >
                    Autoriser la pré-attribution ("Fixer")
                  </Label>
                  <Text className="text-sm text-muted-foreground mt-1">
                    Les invités peuvent réserver des items à l'avance
                  </Text>
                </View>
                <View className="shrink-0 mt-1">
                  <Switch
                    nativeID="preassignLabel"
                    checked={allowPreAssignment}
                    onCheckedChange={setAllowPreAssignment}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Section Participants */}
        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Participants
          </Text>
          <Text className="text-sm text-muted-foreground mb-4">
            Ajoutez les emails des personnes que vous souhaitez inviter
            (optionnel)
          </Text>

          <View className="mb-4">
            <Label
              nativeID="participantEmailLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Email du participant
            </Label>
            <View
              className={Platform.OS === "web" ? "flex-row gap-2" : "gap-2"}
            >
              <View className={Platform.OS === "web" ? "flex-1" : "mb-2"}>
                <Input
                  nativeID="participantEmailLabel"
                  placeholder="<EMAIL>"
                  value={participantEmail}
                  onChangeText={setParticipantEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  className="h-11 border-border"
                  onSubmitEditing={addParticipant}
                />
              </View>
              <Button
                onPress={addParticipant}
                className="h-11 px-4"
                disabled={!participantEmail.trim()}
              >
                <Text className="text-primary-foreground font-medium">
                  Ajouter
                </Text>
              </Button>
            </View>
          </View>

          {/* Liste des participants ajoutés */}
          {participantEmails.length > 0 && (
            <View className="space-y-2">
              <Text className="font-medium text-sm text-foreground">
                Participants invités ({participantEmails.length})
              </Text>
              {participantEmails.map((email, index) => (
                <View
                  key={index}
                  className="flex-row items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <Text className="flex-1 text-sm text-foreground">
                    {email}
                  </Text>
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => removeParticipant(email)}
                    className="ml-2"
                  >
                    <Text className="text-destructive">✕</Text>
                  </Button>
                </View>
              ))}
            </View>
          )}

          <Text className="text-xs text-muted-foreground mt-3">
            💡 Vous pourrez ajouter d'autres participants après la création de
            l'événement
          </Text>
        </View>

        {/* Section Prévisualisation */}
        {title && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Prévisualisation</CardTitle>
            </CardHeader>
            <CardContent>
              <View className="flex-row items-center mb-3">
                <Text className="text-2xl mr-3">{icon || "🎉"}</Text>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-foreground">
                    {title}
                  </Text>
                  <Text className="text-sm text-muted-foreground">
                    {dateTime.toLocaleDateString("fr-FR", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}{" "}
                    à{" "}
                    {dateTime.toLocaleTimeString("fr-FR", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </Text>
                </View>
              </View>
              {location && (
                <Text className="text-sm text-muted-foreground mb-2">
                  📍 {location}
                </Text>
              )}
              {description && (
                <Text className="text-sm text-foreground mb-2">
                  {description}
                </Text>
              )}
              {participantEmails.length > 0 && (
                <Text className="text-sm text-muted-foreground">
                  👥 {participantEmails.length + 1} participant
                  {participantEmails.length > 0 ? "s" : ""} (vous inclus)
                </Text>
              )}
            </CardContent>
          </Card>
        )}

        <Button
          onPress={handleCreateEvent}
          disabled={loading}
          loading={loading}
          className="h-12 bg-primary rounded-lg mb-4"
        >
          <Text className="text-primary-foreground font-medium text-center w-full">
            {loading ? "Création en cours..." : "Créer l'Événement"}
          </Text>
        </Button>
        <Text className="text-sm text-muted-foreground text-center mb-8">
          <Text className="text-destructive">*</Text> Champs obligatoires
        </Text>
      </View>
    </ScrollView>
  );
}
