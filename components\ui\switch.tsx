import * as SwitchPrimitives from "@rn-primitives/switch";
import * as React from "react";
import { Platform, Switch as RNSwitch } from "react-native";

import { useColorScheme } from "~/lib/useColorScheme";
import { cn } from "~/lib/utils";

const SwitchWeb = React.forwardRef<
  SwitchPrimitives.RootRef,
  SwitchPrimitives.RootProps
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer flex-row h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed p-1",
      props.checked ? "bg-yellow-500" : "bg-gray-300",
      props.disabled && "opacity-50",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-4 w-4 rounded-full bg-white web:shadow-md web:shadow-foreground/5 ring-0 transition-transform",
        props.checked ? "translate-x-5" : "translate-x-0"
      )}
    />
  </SwitchPrimitives.Root>
));

SwitchWeb.displayName = "SwitchWeb";

const RGB_COLORS = {
  light: {
    primary: "rgb(245, 158, 11)", // Couleur jaune/orange comme dans l'image
    input: "rgb(209, 213, 219)", // Couleur grise pour l'état inactif
  },
  dark: {
    primary: "rgb(245, 158, 11)", // Même couleur jaune/orange en mode sombre
    input: "rgb(75, 85, 99)", // Couleur grise plus claire pour le mode sombre
  },
} as const;

const SwitchNative = React.forwardRef<
  React.ElementRef<typeof RNSwitch>,
  React.ComponentPropsWithoutRef<typeof RNSwitch>
>(({ className, ...props }, ref) => {
  const { colorScheme } = useColorScheme();

  return (
    <RNSwitch
      ref={ref}
      trackColor={{
        false:
          colorScheme === "dark"
            ? RGB_COLORS.dark.input
            : RGB_COLORS.light.input,
        true:
          colorScheme === "dark"
            ? RGB_COLORS.dark.primary
            : RGB_COLORS.light.primary,
      }}
      thumbColor="#ffffff"
      ios_backgroundColor={
        colorScheme === "dark" ? RGB_COLORS.dark.input : RGB_COLORS.light.input
      }
      {...props}
    />
  );
});
SwitchNative.displayName = "SwitchNative";

const Switch = Platform.select({
  web: SwitchWeb,
  default: SwitchNative,
});

export { Switch };
