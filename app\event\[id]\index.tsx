// --- Begin app/event/[id]/index.tsx ---
import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  ActivityIndicator,
  Platform,
  Modal,
  Pressable,
} from "react-native";
import { useLocalSearchParams, useRouter, useNavigation } from "expo-router";
import { Text } from "~/components/ui/text";
import { P } from "~/components/ui/typography";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  fetchEventDetails,
  updateEvent,
  fetchParticipantsForEvent,
  deleteEvent,
} from "~/lib/supabaseCrud";
import { Separator } from "~/components/ui/separator";
import { Button } from "~/components/ui/button"; // Changed to Button
import { Switch } from "~/components/ui/switch";
import { Label } from "~/components/ui/label";

// Icônes remplacées par des emojis pour compatibilité web
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { EventItemsSection } from "~/components/EventItemsSection"; // Added import

export default function EventDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();
  const navigation = useNavigation();
  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  // Fonction pour charger les détails de l'événement
  const loadEventDetails = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);
      // Charger l'événement et les participants en parallèle
      const [eventDetails, eventParticipants] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
      ]);

      if (eventDetails) {
        setEvent(eventDetails);
        // Le titre est maintenant géré par notre header personnalisé
      }

      setParticipants(eventParticipants || []);
    } catch (error) {
      console.error("Error loading event details:", error);
      showToast("Erreur lors du chargement des détails.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  // Charger les données une seule fois au montage du composant
  useEffect(() => {
    loadEventDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdate = async (field: string, value: any) => {
    if (!event || !session || session.user.id !== event.organizer_id) {
      showToast("Vous n'avez pas les droits pour modifier cet événement.", {
        type: "error",
      });
      return;
    }

    try {
      setUpdateLoading(true);
      const updatedEvent = await updateEvent(event.id, { [field]: value });

      if (updatedEvent) {
        showToast("Option mise à jour !", { type: "success" });
        setEvent(updatedEvent);
      } else {
        throw new Error("Échec de la mise à jour");
      }
    } catch (error) {
      console.error("Error updating event:", error);
      showToast("Erreur lors de la mise à jour.", { type: "error" });
    } finally {
      setUpdateLoading(false);
    }
  };

  // Fonction pour supprimer l'événement
  const handleDeleteEvent = async () => {
    if (!event || !session || session.user.id !== event.organizer_id) {
      showToast("Vous n'avez pas les droits pour supprimer cet événement.", {
        type: "error",
      });
      return;
    }

    try {
      setDeleteLoading(true);
      const success = await deleteEvent(event.id);

      if (success) {
        showToast("Événement supprimé avec succès !", { type: "success" });
        setDeleteModalVisible(false);
        // Retourner à la page d'accueil des événements
        router.push("/(tabs)");
      } else {
        throw new Error("Échec de la suppression");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      showToast("Erreur lors de la suppression.", { type: "error" });
    } finally {
      setDeleteLoading(false);
    }
  };

  // Si l'événement est en cours de chargement, afficher un loader
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" className="text-primary" />
      </View>
    );
  }

  // Si l'événement n'existe pas, afficher un message d'erreur
  if (!event) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">Événement introuvable.</Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const isOrganizer = session?.user?.id === event.organizer_id;

  const eventDate = new Date(event.date_time);
  const formattedDate = eventDate.toLocaleDateString("fr-FR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <View className="flex-1 bg-background">
      {/* Header personnalisé pour mobile avec chevron de retour */}
      {Platform.OS !== "web" && (
        <View className="flex-row items-center px-4 py-4 bg-background border-b border-border">
          <Button
            variant="ghost"
            size="sm"
            onPress={() => router.back()}
            className="mr-3 p-1 min-w-0"
          >
            <Text className="text-2xl font-bold text-primary">‹</Text>
          </Button>
          <Text
            className="text-lg font-semibold text-foreground flex-1"
            numberOfLines={1}
          >
            {event.title}
          </Text>
        </View>
      )}

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
      >
        <View
          className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}
        >
          <Card className="mb-4 border border-border bg-card rounded-xl shadow-sm">
            <CardHeader className="items-center pb-2">
              <Text className="text-6xl mb-2">{event.icon || "🎉"}</Text>
              <CardTitle className="text-2xl text-center font-bold text-foreground">
                {event.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              {event.description && (
                <>
                  <P className="text-center text-muted-foreground">
                    {event.description}
                  </P>
                  <Separator />
                </>
              )}
              <View className="flex-row items-center">
                <Text className="text-muted-foreground mr-3 text-lg">📅</Text>
                <Text className="text-base text-foreground font-medium">
                  {formattedDate}
                </Text>
              </View>
              <View className="flex-row items-center">
                <Text className="text-muted-foreground mr-3 text-lg">🕐</Text>
                <Text className="text-base text-foreground font-medium">
                  {formattedTime}
                </Text>
              </View>
              {event.location && (
                <View className="flex-row items-center">
                  <Text className="text-muted-foreground mr-3 text-lg">📍</Text>
                  <Text className="text-base text-foreground font-medium">
                    {event.location}
                  </Text>
                </View>
              )}
            </CardContent>
          </Card>

          <Card className="mb-4 border border-border bg-card rounded-xl shadow-sm">
            <CardHeader className="flex-row justify-between items-center">
              <CardTitle className="text-lg font-medium text-foreground">
                Actions
              </CardTitle>
              {isOrganizer && (
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => router.push(`/event/${id}/edit`)}
                >
                  <Text>✏️ Éditer</Text>
                </Button>
              )}
            </CardHeader>
            <CardContent className="gap-4">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1 mr-2">
                  <Text className="text-muted-foreground mr-3 text-lg">👥</Text>
                  <Text
                    className="text-foreground font-medium flex-1"
                    numberOfLines={1}
                  >
                    Participants ({participants.length})
                  </Text>
                </View>
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => router.push(`/event/${id}/participants`)}
                  className="shrink-0"
                >
                  <Text>Gérer</Text>
                </Button>
              </View>
              {isOrganizer && (
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center flex-1 mr-2">
                    <Text className="text-muted-foreground mr-3 text-lg">
                      🎯
                    </Text>
                    <Text
                      className="text-foreground font-medium flex-1"
                      numberOfLines={1}
                    >
                      Répartir les tâches
                    </Text>
                  </View>
                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => router.push(`/event/${id}/distribute`)}
                    className="shrink-0"
                  >
                    <Text>Répartir</Text>
                  </Button>
                </View>
              )}
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1 mr-2">
                  <Text className="text-muted-foreground mr-3 text-lg">💬</Text>
                  <Text
                    className="text-foreground font-medium flex-1"
                    numberOfLines={1}
                  >
                    Accéder à la discussion
                  </Text>
                </View>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={true}
                  onPress={() => {}}
                  className="shrink-0"
                >
                  <Text className="text-xs">Bientôt dispo</Text>
                </Button>
              </View>
            </CardContent>
          </Card>

          {/* Item Section Card */}
          {event && id && (
            <EventItemsSection
              eventId={parseInt(id as string, 10)}
              isOrganizer={isOrganizer}
              allowPreAssignment={event.allow_pre_assignment} // Pass event option
            />
          )}

          {isOrganizer && (
            <Card className="mb-6 border border-border bg-card rounded-xl shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg font-medium text-foreground">
                  Options de l'Événement
                </CardTitle>
              </CardHeader>
              <CardContent className="gap-4">
                <View className="flex-row items-center justify-between">
                  <Label
                    nativeID="suggestionsOptLabel"
                    className="flex-1 mr-3 text-base"
                  >
                    Autoriser les suggestions d'items
                  </Label>
                  <View className="shrink-0">
                    {updateLoading ? (
                      <ActivityIndicator
                        size="small"
                        className="text-primary"
                      />
                    ) : (
                      <Switch
                        nativeID="suggestionsOptLabel"
                        checked={event.allow_suggestions}
                        onCheckedChange={(value) =>
                          handleUpdate("allow_suggestions", value)
                        }
                      />
                    )}
                  </View>
                </View>
                <View className="flex-row items-center justify-between">
                  <Label
                    nativeID="preAssignOptLabel"
                    className="flex-1 mr-3 text-base"
                  >
                    Autoriser la pré-attribution ("Fixer")
                  </Label>
                  <View className="shrink-0">
                    {updateLoading ? (
                      <ActivityIndicator
                        size="small"
                        className="text-primary"
                      />
                    ) : (
                      <Switch
                        nativeID="preAssignOptLabel"
                        checked={event.allow_pre_assignment}
                        onCheckedChange={(value) =>
                          handleUpdate("allow_pre_assignment", value)
                        }
                      />
                    )}
                  </View>
                </View>
              </CardContent>
            </Card>
          )}

          {/* Bouton de suppression avec popup de confirmation */}
          {isOrganizer && (
            <Button
              variant="destructive"
              className="w-full h-12 mt-4"
              disabled={deleteLoading}
              onPress={() => setDeleteModalVisible(true)}
            >
              <Text className="mr-2 text-lg">🗑️</Text>
              <Text className="text-white font-medium">
                {deleteLoading ? "Suppression..." : "Supprimer l'événement"}
              </Text>
            </Button>
          )}

          {/* Modal de suppression d'événement */}
          <Modal
            visible={deleteModalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setDeleteModalVisible(false)}
          >
            <View className="flex-1 justify-center items-center bg-black/50">
              <View className="bg-card rounded-lg p-6 mx-4 max-w-sm w-full">
                <Text className="text-lg font-semibold mb-2 text-foreground">
                  Supprimer l'événement
                </Text>
                <Text className="text-muted-foreground mb-6">
                  Cette action est irréversible. Cela supprimera définitivement
                  l'événement "{event?.title}" et toutes les données associées
                  (participants, items, discussions).
                </Text>
                <View className="flex-row justify-end gap-3">
                  <Button
                    variant="outline"
                    onPress={() => setDeleteModalVisible(false)}
                  >
                    <Text>Annuler</Text>
                  </Button>
                  <Button
                    variant="destructive"
                    onPress={handleDeleteEvent}
                    disabled={deleteLoading}
                  >
                    <Text>
                      {deleteLoading ? "Suppression..." : "Supprimer"}
                    </Text>
                  </Button>
                </View>
              </View>
            </View>
          </Modal>

          <View className="h-16" />
        </View>
      </ScrollView>
    </View>
  );
}
// --- End app/event/[id]/index.tsx ---
