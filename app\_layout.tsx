// --- Begin app/_layout.tsx ---
import "~/global.css";
import { enableScreens } from "react-native-screens";
enableScreens();
import {
  DarkTheme,
  DefaultTheme,
  Theme,
  ThemeProvider,
} from "@react-navigation/native";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import * as React from "react";
import { Platform } from "react-native";
import { NAV_THEME } from "~/lib/constants";
import { useColorScheme } from "~/lib/useColorScheme";
import { PortalHost } from "@rn-primitives/portal";

import { AuthProvider } from "~/lib/AuthContext";
import { useToastStore } from "~/lib/toastService";
import { Toast } from "~/components/ui/Toast";

const LIGHT_THEME: Theme = {
  ...DefaultTheme,
  colors: NAV_THEME.light,
};
const DARK_THEME: Theme = {
  ...DarkTheme,
  colors: NAV_THEME.dark,
};

export { ErrorBoundary } from "expo-router";

function useInitialAndroidBarColor() {
  // Fonction simplifiée sans setAndroidNavigationBar
}

function GlobalToast() {
  const { isVisible, message, type, duration, hide } = useToastStore();
  return (
    <Toast
      isVisible={isVisible}
      message={message}
      type={type}
      duration={duration}
      onDismiss={hide}
    />
  );
}

export default function RootLayout() {
  const { colorScheme, isDarkColorScheme } = useColorScheme();
  const [isColorSchemeLoaded, setIsColorSchemeLoaded] = React.useState(false);
  useInitialAndroidBarColor();

  React.useEffect(() => {
    if (Platform.OS === "web") {
      document.documentElement.classList.toggle("dark", isDarkColorScheme);
      document.documentElement.classList.toggle("light", !isDarkColorScheme);
      document.documentElement.classList.add("bg-background");
    }
    setIsColorSchemeLoaded(true);
  }, [isDarkColorScheme]);

  if (!isColorSchemeLoaded) {
    return null;
  }

  return (
    <ThemeProvider value={isDarkColorScheme ? DARK_THEME : LIGHT_THEME}>
      <StatusBar style={isDarkColorScheme ? "light" : "dark"} />
      <AuthProvider>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="event/[id]" // Nom du dossier contenant _layout.tsx
            options={{
              headerShown: false, // Laisser le layout spécifique gérer les headers
            }}
          />
          <Stack.Screen
            name="event/create"
            options={{
              title: "Nouvel Événement",
              presentation: "modal",
              headerBackButtonDisplayMode: "minimal",
              headerBackButtonMenuEnabled: false,
            }}
          />
        </Stack>
      </AuthProvider>
      <PortalHost />
      <GlobalToast />
    </ThemeProvider>
  );
}
// --- End app/_layout.tsx ---
